/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-07-19 17:25:51
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-31 22:42:17
 */
import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        proxy: {
          '/admin-api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/admin-api/, ''),
            // mock代理目标地址
            target: 'http://localhost:48080/admin-api',
            ws: true,
          },
        },
      },
    },
  };
});
